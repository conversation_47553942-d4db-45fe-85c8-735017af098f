# Get current client configuration to retrieve tenant information
data "azuread_client_config" "current" {}

# Get current subscription
data "azurerm_client_config" "current" {}

# Get all subscriptions to assign roles at subscription level (only if needed)
data "azurerm_subscriptions" "available" {
  count = var.assign_roles_to_all_subscriptions ? 1 : 0
}

# Create Azure AD Group for Infrastructure Team Super Admin
resource "azuread_group" "infras_team_super_admin" {
  count = var.create_infras_team_group ? 1 : 0

  display_name     = "Infras Team - Super Admin"
  description      = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
  security_enabled = true

  # Add owners to the group
  owners = [data.azuread_client_config.current.object_id]

  # Add members to the group if specified
  members = var.infras_team_group_members
}

# Assign Owner role to the group at subscription level for all subscriptions
resource "azurerm_role_assignment" "infras_team_owner_all_subs" {
  count = var.create_infras_team_group && var.assign_roles_to_all_subscriptions ? length(data.azurerm_subscriptions.available[0].subscriptions) : 0

  scope                = "/subscriptions/${data.azurerm_subscriptions.available[0].subscriptions[count.index].subscription_id}"
  role_definition_name = "Owner"
  principal_id         = azuread_group.infras_team_super_admin[0].object_id
}

# Assign Owner role to the group at current subscription level only
resource "azurerm_role_assignment" "infras_team_owner_current_sub" {
  count = var.create_infras_team_group && !var.assign_roles_to_all_subscriptions ? 1 : 0

  scope                = "/subscriptions/${data.azurerm_client_config.current.subscription_id}"
  role_definition_name = "Owner"
  principal_id         = azuread_group.infras_team_super_admin[0].object_id
}

# Note: Enterprise Administrator and EA Purchaser roles are Azure AD roles, not Azure RBAC roles
# These need to be assigned through Azure AD, not through Terraform azurerm_role_assignment
# You would need to use azuread_directory_role_assignment for Azure AD roles

# Get Enterprise Administrator directory role
data "azuread_directory_roles" "enterprise_admin" {
  count = var.create_infras_team_group ? 1 : 0
}

# Find the Enterprise Administrator role
locals {
  enterprise_admin_role = var.create_infras_team_group ? [
    for role in data.azuread_directory_roles.enterprise_admin[0].roles :
    role if role.display_name == "Global Administrator" # Enterprise Administrator is now called Global Administrator
  ][0] : null
}

# Assign Enterprise Administrator (Global Administrator) role to the group
resource "azuread_directory_role_assignment" "infras_team_enterprise_admin" {
  count = var.create_infras_team_group ? 1 : 0

  role_id             = local.enterprise_admin_role.object_id
  principal_object_id = azuread_group.infras_team_super_admin[0].object_id
}

# Note: EA Purchaser role is related to Enterprise Agreement billing and is typically managed
# through the Azure EA portal, not through Terraform. This role is for purchasing Azure services
# under an Enterprise Agreement and is managed at the billing account level.

# Output the group information
output "infras_team_super_admin_group" {
  description = "Information about the Infras Team Super Admin group"
  value = var.create_infras_team_group ? {
    object_id    = azuread_group.infras_team_super_admin[0].object_id
    display_name = azuread_group.infras_team_super_admin[0].display_name
    description  = azuread_group.infras_team_super_admin[0].description
  } : null
}

# Output subscription role assignments
output "subscription_role_assignments" {
  description = "List of subscription IDs where Owner role has been assigned"
  value = var.create_infras_team_group ? concat(
    [for assignment in azurerm_role_assignment.infras_team_owner_all_subs : assignment.scope],
    [for assignment in azurerm_role_assignment.infras_team_owner_current_sub : assignment.scope]
  ) : []
}
