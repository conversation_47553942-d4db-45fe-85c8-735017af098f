{"non_productions": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations"], "policy_definitions": ["block_public_ip_assignment_to_vms", "enforce_network_security_groups", "location", "require_mandatory_tags"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Require-Mandatory-Tags": {"tagNames": ["Environment", "CostCenter", "Owner"]}}, "access_control": {}}}}