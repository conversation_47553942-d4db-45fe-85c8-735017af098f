# Use the Azure AD Groups module
module "azure_ad_groups" {
  source = "./modules/azure-ad-groups"

  groups = var.create_infras_team_group ? {
    infras_team_super_admin = {
      display_name     = "Infras Team - Super Admin"
      description      = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
      security_enabled = true
      members          = var.infras_team_group_members
    }
  } : {}

  role_assignments = var.create_infras_team_group ? [
    {
      group_key  = "infras_team_super_admin"
      role_name  = "Owner"
      scope      = "current"
      scope_type = var.assign_roles_to_all_subscriptions ? "all_subscriptions" : "subscription"
    }
  ] : []

  azure_ad_role_assignments = var.create_infras_team_group ? [
    {
      group_key = "infras_team_super_admin"
      role_name = "Global Administrator"
    }
  ] : []

  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
}

# Output the group information
output "infras_team_super_admin_group" {
  description = "Information about the Infras Team Super Admin group"
  value = var.create_infras_team_group ? module.azure_ad_groups.groups["infras_team_super_admin"] : null
}

# Output subscription role assignments
output "subscription_role_assignments" {
  description = "List of subscription IDs where Owner role has been assigned"
  value = module.azure_ad_groups.subscription_role_assignments
}