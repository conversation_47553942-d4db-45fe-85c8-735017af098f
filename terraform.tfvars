# Terraform variables file for Azure AD Groups
# Customize these values according to your organization needs

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Azure AD Groups configuration
groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      # "00000000-0000-0000-0000-000000000000",  # User 1 Object ID
      # "11111111-1111-1111-1111-111111111111",  # User 2 Object ID
    ]
    additional_owners = [
      # "22222222-2222-2222-2222-222222222222",  # Additional Owner Object ID
    ]
  }

  developers = {
    display_name         = "Developers"
    description          = "Development team with contributor access to resources"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # "dev1-object-id",
      # "dev2-object-id"
    ]
  }

  readers = {
    display_name         = "Readers"
    description          = "Read-only access group for monitoring and reporting"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # "reader1-object-id",
      # "reader2-object-id"
    ]
  }

  security_team = {
    display_name         = "Security Team"
    description          = "Security team with security administrator privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Security Administrator"]
    directory_roles      = ["Security Administrator"]
    members = [
      # "security1-object-id",
      # "security2-object-id"
    ]
  }

  network_team = {
    display_name         = "Network Team"
    description          = "Network team with network contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Network Contributor"]
    members = [
      # "network1-object-id",
      # "network2-object-id"
    ]
  }

  database_team = {
    display_name         = "Database Team"
    description          = "Database team with database contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["SQL DB Contributor"]
    members = [
      # "db1-object-id",
      # "db2-object-id"
    ]
  }

  devops_team = {
    display_name         = "DevOps Team"
    description          = "DevOps team with automation and deployment privileges"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor", "User Access Administrator"]
    members = [
      # "devops1-object-id",
      # "devops2-object-id"
    ]
  }

  monitoring_team = {
    display_name         = "Monitoring Team"
    description          = "Monitoring team with monitoring contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Monitoring Contributor"]
    members = [
      # "monitor1-object-id",
      # "monitor2-object-id"
    ]
  }

  backup_team = {
    display_name         = "Backup Team"
    description          = "Backup team with backup operator privileges"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Backup Contributor"]
    members = [
      # "backup1-object-id",
      # "backup2-object-id"
    ]
  }

  compliance_team = {
    display_name         = "Compliance Team"
    description          = "Compliance team with policy contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Policy Contributor"]
    members = [
      # "compliance1-object-id",
      # "compliance2-object-id"
    ]
  }
}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false 