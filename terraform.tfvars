# Multi-Groups Configuration Example
# Copy this to terraform.tfvars and customize with your user object IDs

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Multiple Azure AD Groups configuration
groups = {
  # ===== INFRASTRUCTURE TEAMS =====
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true              # Required for Global Administrator
    azure_roles          = ["Owner", "User Access Administrator"]
    directory_roles      = ["Global Administrator"]
    members = [
      # "admin1-object-id",                  # Replace with actual user object IDs
      # "admin2-object-id",
    ]
    additional_owners = [
      # "manager-object-id",                 # Additional owners if needed
    ]
  }
  
  infras_team_contributors = {
    display_name         = "Infras Team - Contributors"
    description          = "Infrastructure Team with Contributor access to manage resources"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor", "Network Contributor", "Storage Account Contributor"]
    members = []
  }

  infras_team_operation = {
    display_name = "Infras Team - Operation"
    description = " "
    security_enabled = true
    assignable_to_role = false
    azure_roles = ["Reader"]
    members = []
  }

  infras_team_breakglass = {
    display_name = "Infras Team - Breakglass"
    description = ""
    security_enabled = true
    assignable_to_role = false
    azure_roles = ["Owner"]
    members = []
  }

  app_team_super_admin = {
    display_name = "App Teams - Super Admin"
    description = ""
    security_enabled = true
    assignable_to_role = false
    azure_roles = ""
  }
}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false

# How to get user object IDs:
# az ad user show --id <EMAIL> --query id -o tsv
# az ad signed-in-user show --query id -o tsv
# az ad user list --query "[].{DisplayName:displayName, ObjectId:id}" -o table
