# Example Terraform variables file
# Copy this file to terraform.tfvars and customize the values

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Azure AD Group configuration
create_infras_team_group = true

# Add user object IDs to the Infras Team group
# You can get user object IDs from Azure AD portal or using Azure CLI:
# az ad user show --id <EMAIL> --query objectId -o tsv
infras_team_group_members = [
  # "00000000-0000-0000-0000-000000000000",  # User 1 Object ID
  # "11111111-1111-1111-1111-111111111111",  # User 2 Object ID
]

# Role assignment scope
# Set to true to assign Owner role to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false
