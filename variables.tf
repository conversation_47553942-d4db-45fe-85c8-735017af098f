# Use variables to customize the deployment

variable "root_id" {
  type    = string
  default = "myorg"
}

variable "root_name" {
  type    = string
  default = "My Organization"
}

variable "create_infras_team_group" {
  type        = bool
  description = "If set to true, will create the 'Infras Team - Super Admin' Azure AD group with appropriate role assignments."
  default     = true
}

variable "infras_team_group_members" {
  type        = list(string)
  description = "List of user object IDs to add as members to the Infras Team - Super Admin group."
  default     = []
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Owner role to the Infras Team group on all available subscriptions. If false, only assigns to current subscription."
  default     = false
}