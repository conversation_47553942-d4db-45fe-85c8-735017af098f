# Use variables to customize the deployment

variable "root_id" {
  type    = string
  default = "myorg"
}

variable "root_name" {
  type    = string
  default = "My Organization"
}

variable "groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {
    infras_team_super_admin = {
      display_name         = "Infras Team - Super Admin"
      description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Owner"]
      directory_roles      = ["Global Administrator"]
      members              = []
    }
    
    developers = {
      display_name         = "Developers"
      description          = "Development team with contributor access to resources"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Contributor"]
      members              = []
    }
    
    readers = {
      display_name         = "Readers"
      description          = "Read-only access group for monitoring and reporting"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Reader"]
      members              = []
    }
    
    security_team = {
      display_name         = "Security Team"
      description          = "Security team with security administrator privileges"
      security_enabled     = true
      assignable_to_role   = true
      azure_roles          = ["Security Administrator"]
      directory_roles      = ["Security Administrator"]
      members              = []
    }
    
    network_team = {
      display_name         = "Network Team"
      description          = "Network team with network contributor access"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Network Contributor"]
      members              = []
    }
    
    database_team = {
      display_name         = "Database Team"
      description          = "Database team with database contributor access"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["SQL DB Contributor"]
      members              = []
    }
  }
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}